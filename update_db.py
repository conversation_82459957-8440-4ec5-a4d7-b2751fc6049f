from sqlalchemy import Column, DateTime, String, Integer, create_engine, text
from database import Base, engine
from datetime import datetime


def upgrade():
    with engine.connect() as conn:
        # 检查字段是否已存在
        result = conn.execute(text("PRAGMA table_info(users)")).fetchall()
        columns = [row[1] for row in result]

        if 'created_at' not in columns:
            try:
                conn.execute(text("ALTER TABLE users ADD COLUMN created_at DATETIME"))
            except Exception as e:
                print(f"添加 created_at 字段失败: {e}")

        if 'updated_at' not in columns:
            try:
                conn.execute(text("ALTER TABLE users ADD COLUMN updated_at DATETIME"))
            except Exception as e:
                print(f"添加 updated_at 字段失败: {e}")

        if 'status' not in columns:
            try:
                conn.execute(text("ALTER TABLE users ADD COLUMN status VARCHAR(10) DEFAULT '禁用'"))
                print("成功添加 status 字段")
            except Exception as e:
                print(f"添加 status 字段失败: {e}")

        # 为现有记录设置当前时间和默认状态（精确到秒）
        conn.execute(text("UPDATE users SET created_at = strftime('%Y-%m-%d %H:%M:%S', 'now') WHERE created_at IS NULL"))
        conn.execute(text("UPDATE users SET updated_at = strftime('%Y-%m-%d %H:%M:%S', 'now') WHERE updated_at IS NULL"))
        conn.execute(text("UPDATE users SET status = '禁用' WHERE status IS NULL OR status = ''"))
        print("成功更新 users 表结构")

if __name__ == "__main__":
    upgrade()