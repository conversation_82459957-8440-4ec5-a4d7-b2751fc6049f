#!/usr/bin/env python3
"""
测试新的任务ID生成方式
验证T前缀+时间戳+随机数的ID生成是否正常工作
"""

import random
from datetime import datetime

def generate_task_id():
    """生成新的任务ID：T + 时间戳 + 随机数"""
    return f"T{int(datetime.now().timestamp() * 1000)}{random.randint(1000, 9999)}"

def generate_user_id():
    """生成用户ID：时间戳 + 随机数"""
    return f"{int(datetime.now().timestamp() * 1000)}{random.randint(1000, 9999)}"

def test_id_generation():
    """测试ID生成"""
    print("=== 测试新的任务ID生成方式 ===")
    print("格式：T + 时间戳(毫秒) + 随机数(4位)")
    print()
    
    # 生成10个示例任务ID
    task_ids = []
    for i in range(10):
        task_id = generate_task_id()
        task_ids.append(task_id)
        print(f"任务ID {i+1:2d}: {task_id}")
    
    print()
    print("=== 任务ID长度统计 ===")
    lengths = [len(id) for id in task_ids]
    print(f"最短长度: {min(lengths)}")
    print(f"最长长度: {max(lengths)}")
    print(f"平均长度: {sum(lengths)/len(lengths):.1f}")
    
    print()
    print("=== 与用户ID对比 ===")
    user_id = generate_user_id()
    task_id = generate_task_id()
    
    print(f"用户ID: {user_id}")
    print(f"任务ID: {task_id}")
    print(f"用户ID长度: {len(user_id)}")
    print(f"任务ID长度: {len(task_id)}")
    
    print()
    print("=== ID格式验证 ===")
    for i, tid in enumerate(task_ids[:3], 1):
        print(f"任务ID {i}: {tid}")
        if tid.startswith('T') and len(tid) == 18 and tid[1:].isdigit():
            timestamp_part = tid[1:14]
            random_part = tid[14:]
            print(f"  ✅ 格式正确 - 前缀:T, 时间戳:{timestamp_part}, 随机数:{random_part}")
        else:
            print(f"  ❌ 格式错误")
    
    print()
    print("=== 与旧方式对比 ===")
    import uuid
    old_task_id = f"{int(datetime.now().timestamp() * 1000)}-{uuid.uuid4().hex}"
    new_task_id = generate_task_id()
    
    print(f"旧方式 (时间戳-UUID): {old_task_id}")
    print(f"新方式 (T+时间戳+随机数): {new_task_id}")
    print(f"长度减少: {len(old_task_id) - len(new_task_id)} 个字符")
    print(f"长度减少比例: {((len(old_task_id) - len(new_task_id)) / len(old_task_id) * 100):.1f}%")
    
    print()
    print("=== 优势总结 ===")
    print("✅ 易于识别：T前缀明确标识任务ID")
    print("✅ 调试友好：在日志、URL中容易区分")
    print("✅ 保持唯一性：时间戳+随机数确保唯一性")
    print("✅ 长度适中：18位，比UUID方案短很多")

if __name__ == "__main__":
    test_id_generation()
