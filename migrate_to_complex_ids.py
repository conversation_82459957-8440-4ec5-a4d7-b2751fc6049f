#!/usr/bin/env python3
"""
数据库迁移脚本：将用户ID从整数类型迁移到复杂字符串类型
"""

import sqlite3
import uuid
from datetime import datetime
import os
import shutil

def create_complex_id():
    """生成复杂的ID"""
    return f"{int(datetime.now().timestamp() * 1000)}-{uuid.uuid4().hex}"

def migrate_database():
    """迁移数据库"""
    db_path = "backend.db"
    backup_path = f"backend_backup_{int(datetime.now().timestamp())}.db"
    
    # 备份原数据库
    if os.path.exists(db_path):
        shutil.copy2(db_path, backup_path)
        print(f"数据库已备份到: {backup_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查是否已经是新结构
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        id_column = next((col for col in columns if col[1] == 'id'), None)
        
        if id_column and id_column[2] == 'VARCHAR(50)':
            print("数据库已经是新结构，无需迁移")
            return
        
        print("开始迁移数据库...")
        
        # 1. 获取现有用户数据
        cursor.execute("SELECT * FROM users")
        existing_users = cursor.fetchall()
        
        # 2. 获取现有项目数据
        cursor.execute("SELECT * FROM projects")
        existing_projects = cursor.fetchall()
        
        # 3. 获取现有任务数据
        cursor.execute("SELECT * FROM tasks")
        existing_tasks = cursor.fetchall()
        
        # 4. 创建ID映射
        user_id_mapping = {}
        project_id_mapping = {}
        task_id_mapping = {}
        
        # 为用户创建新ID
        for user in existing_users:
            old_id = user[0]
            new_id = create_complex_id()
            user_id_mapping[old_id] = new_id
        
        # 为项目创建新ID
        for project in existing_projects:
            old_id = project[0]
            new_id = create_complex_id()
            project_id_mapping[old_id] = new_id
        
        # 为任务创建新ID
        for task in existing_tasks:
            old_id = task[0]
            new_id = create_complex_id()
            task_id_mapping[old_id] = new_id
        
        # 5. 删除旧表
        cursor.execute("DROP TABLE IF EXISTS users_old")
        cursor.execute("DROP TABLE IF EXISTS projects_old")
        cursor.execute("DROP TABLE IF EXISTS tasks_old")
        
        # 重命名现有表
        cursor.execute("ALTER TABLE users RENAME TO users_old")
        cursor.execute("ALTER TABLE projects RENAME TO projects_old")
        cursor.execute("ALTER TABLE tasks RENAME TO tasks_old")
        
        # 6. 创建新表结构
        cursor.execute("""
            CREATE TABLE users (
                id VARCHAR(50) PRIMARY KEY,
                username VARCHAR(50) UNIQUE,
                role VARCHAR(50),
                password VARCHAR(100),
                status VARCHAR(10) DEFAULT '禁用',
                created_at DATETIME,
                updated_at DATETIME,
                deleted BOOLEAN DEFAULT 0
            )
        """)
        
        cursor.execute("""
            CREATE TABLE projects (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(255),
                description TEXT,
                start_date DATE,
                end_date DATE,
                target INTEGER,
                owner_id VARCHAR(50),
                progress FLOAT DEFAULT 0.0,
                FOREIGN KEY (owner_id) REFERENCES users (id)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE tasks (
                id VARCHAR(50) PRIMARY KEY,
                project_id VARCHAR(50),
                name VARCHAR(255),
                priority VARCHAR(50),
                progress FLOAT DEFAULT 0.0,
                start_date DATE,
                end_date DATE,
                owner_id VARCHAR(50),
                related_task VARCHAR(50),
                FOREIGN KEY (project_id) REFERENCES projects (id),
                FOREIGN KEY (owner_id) REFERENCES users (id),
                FOREIGN KEY (related_task) REFERENCES tasks (id)
            )
        """)
        
        # 7. 迁移用户数据
        for user in existing_users:
            old_id, username, role, password, deleted, created_at, updated_at, status = user
            new_id = user_id_mapping[old_id]
            
            cursor.execute("""
                INSERT INTO users (id, username, role, password, status, created_at, updated_at, deleted)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (new_id, username, role, password, status, created_at, updated_at, deleted))
        
        # 8. 迁移项目数据
        for project in existing_projects:
            old_id, name, description, start_date, end_date, target, old_owner_id, progress = project
            new_id = project_id_mapping[old_id]
            new_owner_id = user_id_mapping.get(old_owner_id) if old_owner_id else None
            
            cursor.execute("""
                INSERT INTO projects (id, name, description, start_date, end_date, target, owner_id, progress)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (new_id, name, description, start_date, end_date, target, new_owner_id, progress))
        
        # 9. 迁移任务数据
        for task in existing_tasks:
            old_id, old_project_id, name, priority, progress, start_date, end_date, old_owner_id, old_related_task = task
            new_id = task_id_mapping[old_id]
            new_project_id = project_id_mapping.get(old_project_id) if old_project_id else None
            new_owner_id = user_id_mapping.get(old_owner_id) if old_owner_id else None
            new_related_task = task_id_mapping.get(old_related_task) if old_related_task else None
            
            cursor.execute("""
                INSERT INTO tasks (id, project_id, name, priority, progress, start_date, end_date, owner_id, related_task)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (new_id, new_project_id, name, priority, progress, start_date, end_date, new_owner_id, new_related_task))
        
        # 10. 删除旧表
        cursor.execute("DROP TABLE users_old")
        cursor.execute("DROP TABLE projects_old")
        cursor.execute("DROP TABLE tasks_old")
        
        conn.commit()
        print("数据库迁移完成！")
        print(f"迁移了 {len(existing_users)} 个用户")
        print(f"迁移了 {len(existing_projects)} 个项目")
        print(f"迁移了 {len(existing_tasks)} 个任务")
        
        # 显示ID映射示例
        if user_id_mapping:
            print("\n用户ID映射示例:")
            for old_id, new_id in list(user_id_mapping.items())[:3]:
                print(f"  {old_id} -> {new_id}")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        conn.rollback()
        # 恢复备份
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, db_path)
            print("已恢复数据库备份")
        raise
    
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
