#!/usr/bin/env python3
"""
测试新的用户创建功能
验证使用新ID生成方式的用户创建是否正常
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from database import SessionLocal, init_db
from models import User
from schemas import UserCreate
import crud

def test_user_creation():
    """测试用户创建"""
    print("=== 测试新的用户创建功能 ===")
    
    # 初始化数据库
    init_db()
    
    # 创建数据库会话
    db: Session = SessionLocal()
    
    try:
        # 创建测试用户数据
        test_user_data = UserCreate(
            username="test_user_new_id",
            password="test123456",
            role="user",
            status="启用"
        )
        
        print(f"创建用户: {test_user_data.username}")
        
        # 创建用户
        new_user = crud.create_user(db, test_user_data)
        
        print(f"✅ 用户创建成功!")
        print(f"用户ID: {new_user.id}")
        print(f"用户名: {new_user.username}")
        print(f"角色: {new_user.role}")
        print(f"状态: {new_user.status}")
        
        # 验证ID格式
        id_parts = new_user.id.split('-')
        if len(id_parts) == 2:
            timestamp_part = id_parts[0]
            random_part = id_parts[1]
            
            print(f"\nID格式验证:")
            print(f"时间戳部分: {timestamp_part} (长度: {len(timestamp_part)})")
            print(f"随机数部分: {random_part} (长度: {len(random_part)})")
            print(f"总长度: {len(new_user.id)}")
            
            if len(random_part) == 4 and random_part.isdigit():
                print("✅ ID格式正确: 时间戳-4位随机数")
            else:
                print("❌ ID格式错误")
        else:
            print("❌ ID格式错误: 应该包含一个连字符")
            
        # 测试查询用户
        print(f"\n=== 测试用户查询 ===")
        found_user = crud.get_user(db, new_user.id)
        if found_user:
            print(f"✅ 用户查询成功: {found_user.username}")
        else:
            print("❌ 用户查询失败")
            
        # 清理测试数据
        crud.delete_user(db, new_user.id)
        print(f"✅ 测试用户已删除")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_user_creation()
