from fastapi import Depends, FastAPI, HTTPException
from contextlib import asynccontextmanager
from database import init_db, async_init_db
import models
import routers.projects
import routers.tasks
import routers.users
import uvicorn
import asyncio
from fastapi.middleware.cors import CORSMiddleware
import os

# 添加JWT配置
SECRET_KEY = "YOUR_SECRET_KEY_HERE"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

app = FastAPI()

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    await async_init_db()

# 注册路由
app.include_router(routers.projects.router)
app.include_router(routers.tasks.router)
app.include_router(routers.users.router)

if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)