import uuid
import random
from sqlalchemy import <PERSON><PERSON>n, Integer, String, DateTime, Date, Time, Foreign<PERSON><PERSON>, <PERSON>ole<PERSON>, Float
from sqlalchemy.orm import Session, relationship
import datetime
from typing import Optional
from database import Base
import jwt  # Added jwt import
from passlib.context import CryptContext
# 从datetime导入timedelta
from datetime import timedelta, datetime as dt

detail_response_code = "detail_response_code"

class DetailResponse:
    def __init__(self, code: int, msg: str, data: Optional[dict]):
        self.code = code
        self.msg = msg
        self.data = data

# 创建密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = dt.utcnow() + expires_delta
        to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, "SECRET_KEY", algorithm="HS256")
    return encoded_jwt

def authenticate_user(db: Session, username: str, password: str):
    user = db.query(User).filter(User.username == username).first()  # 修改为username
    if not user or not pwd_context.verify(password, str(user.password)):
        return DetailResponse(code=401, msg="账户密码不可用", data=None)
    
    # 创建JWT token
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": user.username, "id": user.id},  # 修改为username
        expires_delta=access_token_expires
    )
    
    # 返回包含token的响应
    user_data = user.to_dict()
    user_data["access_token"] = access_token
    user_data["token_type"] = "bearer"
    return DetailResponse(code=200, msg="登陆成功", data=user_data)


class User(Base):
    __tablename__ = "users"
    id = Column(String(50), primary_key=True, default=lambda: f"{int(dt.now().timestamp() * 1000)}-{random.randint(1000, 9999)}")
    username = Column(String(50), unique=True, index=True)
    role = Column(String(50))
    password = Column(String(100))
    status = Column(String(10), default="禁用")
    created_at = Column(DateTime, default=dt.now)
    updated_at = Column(DateTime, default=dt.now, onupdate=dt.now)
    deleted = Column(Boolean, default=False)
    projects = relationship("Project", back_populates="owner")
    tasks = relationship("Task", back_populates="owner")

    def to_dict(self):
        def fmt(dt):
            if dt is None:
                return None
            return dt.strftime('%Y-%d-%m %H:%M:%S') if hasattr(dt, 'strftime') else str(dt)
        return {
            "id": self.id,
            "username": self.username,
            "role": self.role,
            "status": self.status,
            "deleted": self.deleted,
            "created_at": fmt(self.created_at),
            "updated_at": fmt(self.updated_at)
        }

    def to_token_dict(self):
        return {
            "id": self.id,
            "name": self.username,  # 修改为username
            "role": self.role
        }


class Project(Base):
    __tablename__ = "projects"
    
    # 重写id字段，使用时间戳+随机值生成，确保时间戳精确到毫秒
    id = Column(String(50), primary_key=True, default=lambda: f"{int(dt.now().timestamp() * 1000)}-{uuid.uuid4().hex}")
    
    name = Column(String, index=True)
    description = Column(String)
    start_date = Column(Date)
    end_date = Column(Date)
    target = Column(Integer)
    owner_id = Column(String(50), ForeignKey("users.id"))
    progress = Column(Float, default=0.0)
    owner = relationship("User", back_populates="projects")
    tasks = relationship("Task", back_populates="project")

    def to_dict(self):
        def fmt(dt):
            if dt is None:
                return None
            return dt.strftime('%Y-%d-%m %H:%M:%S') if hasattr(dt, 'strftime') else str(dt)
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "start_date": fmt(self.start_date),
            "end_date": fmt(self.end_date),
            "target": self.target,
            "owner_id": self.owner_id,
            "progress": self.progress
        }


class Task(Base):
    __tablename__ = "tasks"
    
    # 重写id字段，使用时间戳+随机值生成，确保时间戳精确到毫秒
    id = Column(String(50), primary_key=True, default=lambda: f"{int(dt.now().timestamp() * 1000)}-{uuid.uuid4().hex}")
    
    project_id = Column(String(50), ForeignKey("projects.id"))  # 修改为String类型
    name = Column(String, index=True)
    priority = Column(String, index=True)
    progress = Column(Float, default=0.0)
    start_date = Column(Date)
    end_date = Column(Date)
    owner_id = Column(String(50), ForeignKey("users.id"))
    related_task = Column(String(50), ForeignKey("tasks.id"), nullable=True)
    project = relationship("Project", back_populates="tasks")
    owner = relationship("User", back_populates="tasks")

    def to_dict(self):
        def fmt(dt):
            if dt is None:
                return None
            return dt.strftime('%Y-%d-%m %H:%M:%S') if hasattr(dt, 'strftime') else str(dt)
        return {
            "id": self.id,
            "project_id": self.project_id,
            "name": self.name,
            "priority": self.priority,
            "progress": self.progress,
            "start_date": fmt(self.start_date),
            "end_date": fmt(self.end_date),
            "owner_id": self.owner_id,
            "related_task": self.related_task
        }


class Merchant(Base):
    __tablename__ = "merchants"
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    password = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }
