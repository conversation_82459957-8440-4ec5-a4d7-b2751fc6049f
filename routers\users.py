from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.orm import Session
from schemas import  ListResponse, DetailResponse, UserCreate, LoginCredentials
from crud import get_users, create_user, update_user, delete_user as crud_delete_user, get_merchant
from models import authenticate_user, User
from database import get_db
import crud

# 配置JWT
SECRET_KEY = "your-secret-key"  # 在生产环境中应该使用安全的密钥
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def get_current_merchant(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        merchant_id = payload.get("sub")
        if merchant_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
        
    merchant = crud.get_merchant(db, merchant_id=int(merchant_id))
    if merchant is None:
        raise credentials_exception
    return merchant

router = APIRouter(
    prefix="/users",
    tags=["users"],
)

# 删除用户
@router.delete("/{user_id}", response_model=DetailResponse)
def delete_user_route(user_id: str, db: Session = Depends(get_db)):
    result = crud_delete_user(db, user_id)
    if result:
        return DetailResponse(code=200, msg="用户删除成功", data=result.to_dict())
    else:
        return DetailResponse(code=404, msg="用户不存在", data=None)

@router.get("/list", response_model=ListResponse)
def read_users(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    users = get_users(db, skip=skip, limit=limit)
    # 转换为字典格式
    user_data = [user.to_dict() for user in users]
    return ListResponse(code=200, msg="Success", data=user_data)

@router.post("/create", response_model=DetailResponse)
def create_user_route(user: UserCreate, db: Session = Depends(get_db)):
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == user.username).first()
        if existing_user:
            return DetailResponse(code=400, msg="用户名已存在", data=None)

        result = create_user(db, user)
        return DetailResponse(code=200, msg="用户创建成功", data=result.to_dict())
    except Exception as e:
        return DetailResponse(code=500, msg=f"用户创建失败: {str(e)}", data=None)

# 查看用户信息
@router.get("/info", response_model=DetailResponse)
def get_user_info(user_id: str, db: Session = Depends(get_db)):
    user = crud.get_user(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    return DetailResponse(code=200, msg="Success", data=user.to_dict())

# 更新用户信息
@router.put("/info", response_model=DetailResponse)
def update_user_route(user_id: str, user_update: UserCreate, db: Session = Depends(get_db)):
    result = update_user(user_id, user_update, db)
    if result:
        return DetailResponse(code=200, msg="用户更新成功", data=result.to_dict())
    else:
        return DetailResponse(code=404, msg="用户不存在", data=None)

@router.post("/login", response_model=DetailResponse)
def login(credentials: LoginCredentials, db: Session = Depends(get_db)):
    # 执行认证
    result = authenticate_user(db, username=credentials.username, password=credentials.password)
    # 转换为Pydantic模型格式
    return DetailResponse(code=result.code, msg=result.msg, data=result.data)

@router.post("/logout", response_model=DetailResponse)
def logout(token: str = Depends(oauth2_scheme)):
    """
    用户退出登录
    - 验证token有效性
    - 由于使用的是无状态的JWT认证，退出登录只需前端清除token即可
    """
    try:
        # 验证token（可选，确保是有效用户发起的退出请求）
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if username is None:
            return DetailResponse(code=401, msg="无效的认证凭证", data=None)

        # 退出成功
        return DetailResponse(code=200, msg="成功登出", data={"username": username})

    except JWTError:
        # 即使token无效，也允许退出（前端清除token）
        return DetailResponse(code=200, msg="成功登出", data=None)