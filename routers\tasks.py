from fastapi import APIRouter, Depends, HTTPException
from typing import Optional
from datetime import datetime
from sqlalchemy.orm import Session
from database import get_db
from routers.users import get_current_merchant  # 从users模块导入认证函数
import crud, schemas

router = APIRouter(
    prefix="/tasks",
    tags=["tasks"]
)

@router.get("/list", response_model=schemas.ListResponse)
def read_tasks(skip: int = 0, limit: int = 100, project_id: Optional[str] = None, db: Session = Depends(get_db)):
    tasks = crud.get_tasks(db, skip=skip, limit=limit, project_id=project_id)
    return schemas.ListResponse(code=200, msg="Success", data=[schemas.Task.model_validate(task).model_dump() for task in tasks])

@router.get("/{task_id}", response_model=schemas.DetailResponse)
def read_task(task_id: str, db: Session = Depends(get_db)):
    task = crud.get_task(db, task_id=task_id)
    if task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    return schemas.DetailResponse(code=200, msg="Success", data=schemas.Task.model_validate(task).model_dump())

@router.post("/add", response_model=schemas.DetailResponse)
def create_task(task: schemas.TaskCreate, db: Session = Depends(get_db)):
    db_task = crud.create_task(db, task=task)
    return schemas.DetailResponse(code=201, msg="Task created", data=schemas.Task.model_validate(db_task).model_dump())

@router.put("/{task_id}", response_model=schemas.DetailResponse)
def update_task(task_id: str, task: schemas.TaskUpdate, db: Session = Depends(get_db)):
    db_task = crud.update_task(task_id=task_id, task=task, db=db)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    return schemas.DetailResponse(code=200, msg="Task updated", data=schemas.Task.model_validate(db_task).model_dump())

@router.delete("/{task_id}", response_model=schemas.DetailResponse)
def delete_task(task_id: str, db: Session = Depends(get_db)):
    db_task = crud.delete_task(db=db, task_id=task_id)
    if db_task is None:
        raise HTTPException(status_code=404, detail="Task not found")
    return schemas.DetailResponse(code=200, msg="Task deleted", data=schemas.Task.model_validate(db_task).model_dump())

@router.get("/stats", response_model=schemas.TaskStatsResponse)
def get_task_stats(
    time_range: schemas.TimeRange,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_merchant: dict = Depends(get_current_merchant),
    db: Session = Depends(get_db)
):
    """
    获取任务统计信息
    - time_range: 时间范围(today/week/month/custom)
    - start_date: 自定义开始时间(可选)
    - end_date: 自定义结束时间(可选)
    """
    if time_range == schemas.TimeRange.CUSTOM and (not start_date or not end_date):
        raise HTTPException(status_code=400, detail="Custom time range requires both start_date and end_date")
        
    stats = crud.get_task_stats(
        db=db,
        merchant_id=current_merchant['id'],
        time_range=time_range,
        start_date=start_date,
        end_date=end_date
    )
    return stats