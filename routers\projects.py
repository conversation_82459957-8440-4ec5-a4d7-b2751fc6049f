from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

import crud
from database import get_db
from schemas import DetailResponse, ListResponse, ProjectCreate, ProjectUpdate

router = APIRouter(
    prefix="/projects",
    tags=["projects"]
)

# 新增项目
@router.post("/add", response_model=DetailResponse)
def create_project_endpoint(project: ProjectCreate, db: Session = Depends(get_db)):
    db_project = crud.create_project(db, project)
    return {"code": 201, "msg": "Project created successfully", "data": db_project}

# 获取项目列表
@router.get("/list", response_model=ListResponse)
def get_projects_endpoint(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    projects = crud.get_projects(db, skip, limit)
    return {"code": 200, "msg": "Projects retrieved successfully", "data": projects}

# 获取单个项目详情
@router.get("/{project_id}", response_model=DetailResponse)
def get_project_endpoint(project_id: str, db: Session = Depends(get_db)):
    db_project = crud.get_project(db, project_id)
    if not db_project:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"code": 200, "msg": "Project retrieved successfully", "data": db_project}

# 更新项目
@router.put("/{project_id}", response_model=DetailResponse)
def update_project_endpoint(project_id: str, project: ProjectUpdate, db: Session = Depends(get_db)):
    db_project = crud.get_project(db, project_id)
    if not db_project:
        raise HTTPException(status_code=404, detail="Project not found")
    for key, value in project.dict().items():
        setattr(db_project, key, value)
    db.commit()
    db.refresh(db_project)
    return {"code": 200, "msg": "Project updated successfully", "data": db_project}

# 删除项目
@router.delete("/{project_id}", response_model=DetailResponse)
def delete_project_endpoint(project_id: str, db: Session = Depends(get_db)):
    success = crud.delete_project(db, project_id)
    if not success:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"code": 200, "msg": "Project deleted successfully", "data": None}

