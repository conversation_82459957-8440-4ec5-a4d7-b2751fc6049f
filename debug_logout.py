#!/usr/bin/env python3
"""
调试退出接口
"""
import urllib.request
import urllib.parse
import json

def debug_logout():
    """调试退出接口"""
    # 首先登录获取token
    login_url = "http://127.0.0.1:8000/users/login"
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    json_data = json.dumps(login_data).encode('utf-8')
    req = urllib.request.Request(login_url, data=json_data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        with urllib.request.urlopen(req) as response:
            response_data = response.read().decode('utf-8')
            login_response = json.loads(response_data)
            
            if login_response.get('code') == 200:
                token = login_response['data']['access_token']
                print(f"登录成功，获取到token: {token}")
                
                # 解析token内容
                import base64
                try:
                    # JWT token格式: header.payload.signature
                    parts = token.split('.')
                    if len(parts) == 3:
                        # 解码payload部分
                        payload = parts[1]
                        # 添加padding如果需要
                        padding = 4 - len(payload) % 4
                        if padding != 4:
                            payload += '=' * padding
                        
                        decoded_payload = base64.b64decode(payload)
                        payload_json = json.loads(decoded_payload)
                        print(f"Token payload: {json.dumps(payload_json, indent=2)}")
                except Exception as e:
                    print(f"解析token失败: {e}")
                
                # 使用token调用退出接口
                logout_url = "http://127.0.0.1:8000/users/logout"
                logout_req = urllib.request.Request(logout_url, data=b'', method='POST')
                logout_req.add_header('Content-Type', 'application/json')
                logout_req.add_header('Authorization', f'Bearer {token}')
                
                print(f"发送退出请求到: {logout_url}")
                print(f"Authorization header: Bearer {token[:50]}...")
                
                with urllib.request.urlopen(logout_req) as logout_response:
                    logout_data = logout_response.read().decode('utf-8')
                    print(f"退出接口状态码: {logout_response.status}")
                    print(f"退出接口原始响应: {logout_data}")
                    
                    logout_json = json.loads(logout_data)
                    print(f"退出接口解析后响应: {json.dumps(logout_json, indent=2, ensure_ascii=False)}")
            else:
                print(f"登录失败: {login_response}")
                
    except Exception as e:
        print(f"调试退出时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_logout()
