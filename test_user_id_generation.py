#!/usr/bin/env python3
"""
测试新的用户ID生成方式
验证时间戳+随机数的ID生成是否正常工作
"""

import random
from datetime import datetime

def generate_user_id():
    """生成新的用户ID：时间戳+随机数"""
    return f"{int(datetime.now().timestamp() * 1000)}{random.randint(1000, 9999)}"

def test_id_generation():
    """测试ID生成"""
    print("=== 测试新的用户ID生成方式 ===")
    print("格式：时间戳(毫秒)+随机数(4位)")
    print()
    
    # 生成10个示例ID
    ids = []
    for i in range(10):
        user_id = generate_user_id()
        ids.append(user_id)
        print(f"ID {i+1:2d}: {user_id}")
    
    print()
    print("=== ID长度统计 ===")
    lengths = [len(id) for id in ids]
    print(f"最短长度: {min(lengths)}")
    print(f"最长长度: {max(lengths)}")
    print(f"平均长度: {sum(lengths)/len(lengths):.1f}")
    
    print()
    print("=== 与旧方式对比 ===")
    import uuid
    old_id = f"{int(datetime.now().timestamp() * 1000)}-{uuid.uuid4().hex}"
    new_id = generate_user_id()

    print(f"旧方式 (时间戳-UUID): {old_id}")
    print(f"新方式 (时间戳+随机数): {new_id}")
    print(f"长度减少: {len(old_id) - len(new_id)} 个字符")
    print(f"长度减少比例: {((len(old_id) - len(new_id)) / len(old_id) * 100):.1f}%")

if __name__ == "__main__":
    test_id_generation()
