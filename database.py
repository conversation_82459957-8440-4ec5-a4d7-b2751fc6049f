from sqlalchemy import create_engine, text  # 导入缺失的text函数
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import random
from datetime import datetime
import asyncio

# 创建时间戳+随机数模式的引擎
def create_uuid_timestamp_engine():
    def id_generator():
        return f"{int(datetime.now().timestamp() * 1000)}{random.randint(1000, 9999)}"
    
    engine = create_engine(
        "sqlite:///./backend.db",  # 使用SQLite作为示例，可以根据需要更改
        connect_args={"check_same_thread": False},  # SQLite专用参数
        pool_pre_ping=True,
        pool_recycle=300
    )
    return engine

SQLALCHEMY_DATABASE_URL = "sqlite:///./backend.db"  # 修改数据库文件名为backend.db

engine = create_uuid_timestamp_engine()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# 创建数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 初始化数据库（创建所有表）
def init_db():
    from models import Base, User
    Base.metadata.create_all(bind=engine)
    print("数据库表已初始化")

    # 创建默认admin用户
    db = SessionLocal()
    try:
        # 检查是否已经存在admin用户
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if not existing_admin:
            from passlib.context import CryptContext
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            
            current_time = datetime.utcnow()
            default_user = User(
                username="admin",
                role="admin",
                password=pwd_context.hash("123456"),  # 密码加密存储
                created_at=current_time,
                updated_at=current_time
            )
            db.add(default_user)
            db.commit()
            print("默认admin用户已创建")
        else:
            print("admin用户已存在，跳过创建")
    finally:
        db.close()

# 异步版本的初始化数据库
async def async_init_db():
    await asyncio.get_event_loop().run_in_executor(None, init_db)

# 检查数据库表结构
def check_table_structure(db_path, table_name):
    from check_db import check_table_structure
    check_table_structure(db_path, table_name)
    print(f"{table_name} 表结构检查完成")

# 检查数据库连接是否正常
def check_db_connection():
    try:
        with engine.connect() as conn:
            print("成功连接到数据库")
            result = conn.execute(text("SELECT 1"))
            print(f"数据库查询测试成功：{result}")
            return True
    except Exception as e:
        print(f"无法连接到数据库: {e}")
        return False

if __name__ == "__main__":
    init_db()