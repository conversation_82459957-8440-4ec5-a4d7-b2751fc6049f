#!/usr/bin/env python3
"""
测试使用新项目ID的项目CRUD操作
验证项目的创建、查询、更新、删除功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from database import SessionLocal, init_db
from models import Project, User
from schemas import ProjectCreate, ProjectUpdate
import crud

def test_project_crud():
    """测试项目CRUD操作"""
    print("=== 测试新项目ID的CRUD操作 ===")
    
    # 初始化数据库
    init_db()
    
    # 创建数据库会话
    db: Session = SessionLocal()
    
    try:
        # 首先创建一个测试用户（如果不存在）
        test_user = db.query(User).filter(User.username == "test_user_project").first()
        if not test_user:
            from schemas import UserCreate
            test_user_data = UserCreate(
                username="test_user_project",
                password="test123",
                role="user"
            )
            test_user = crud.create_user(db, test_user_data)
            print(f"✅ 创建测试用户: {test_user.id}")
        
        # 创建测试项目数据
        test_project_data = ProjectCreate(
            name="测试项目 - 新ID格式",
            description="这是一个使用新ID格式的测试项目",
            target=100,
            progress=0.0,
            owner_id=test_user.id
        )
        
        print(f"\n=== 1. 创建项目 ===")
        print(f"项目名称: {test_project_data.name}")
        print(f"项目描述: {test_project_data.description}")
        print(f"负责人: {test_user.username}")
        
        # 创建项目
        new_project = crud.create_project(db, test_project_data)
        
        print(f"✅ 项目创建成功!")
        print(f"项目ID: {new_project.id}")
        print(f"项目名称: {new_project.name}")
        print(f"项目描述: {new_project.description}")
        print(f"负责人ID: {new_project.owner_id}")
        
        # 验证项目ID格式
        print(f"\n=== 2. 验证项目ID格式 ===")
        project_id = new_project.id
        if project_id.startswith('P') and len(project_id) == 18 and project_id[1:].isdigit():
            timestamp_part = project_id[1:14]
            random_part = project_id[14:]
            print(f"✅ 项目ID格式正确")
            print(f"  完整ID: {project_id}")
            print(f"  前缀: P")
            print(f"  时间戳: {timestamp_part}")
            print(f"  随机数: {random_part}")
        else:
            print(f"❌ 项目ID格式错误: {project_id}")
            
        # 测试查询项目
        print(f"\n=== 3. 查询项目 ===")
        found_project = crud.get_project(db, project_id)
        if found_project:
            print(f"✅ 项目查询成功: {found_project.name}")
            print(f"  项目ID: {found_project.id}")
        else:
            print("❌ 项目查询失败")
            
        # 测试更新项目
        print(f"\n=== 4. 更新项目 ===")
        update_data = ProjectUpdate(
            name="更新后的项目名称",
            description="更新后的项目描述",
            target=200,
            progress=25.0,
            owner_id=test_user.id
        )
        
        updated_project = crud.update_project(project_id, update_data, db)
        if updated_project:
            print(f"✅ 项目更新成功")
            print(f"  新名称: {updated_project.name}")
            print(f"  新描述: {updated_project.description}")
            print(f"  新目标: {updated_project.target}")
            print(f"  新进度: {updated_project.progress}%")
        else:
            print("❌ 项目更新失败")
            
        # 测试项目列表查询
        print(f"\n=== 5. 查询项目列表 ===")
        projects = crud.get_projects(db, skip=0, limit=10)
        print(f"✅ 查询到 {len(projects)} 个项目")
        for i, project in enumerate(projects[:3], 1):  # 只显示前3个
            print(f"  项目{i}: {project.id} - {project.name}")
            
        # 测试创建关联任务
        print(f"\n=== 6. 创建关联任务 ===")
        from schemas import TaskCreate
        test_task_data = TaskCreate(
            project_id=project_id,  # 使用新的项目ID
            name="测试任务 - 关联到新项目",
            priority="高",
            progress=0.0,
            owner_id=test_user.id
        )
        
        new_task = crud.create_task(db, test_task_data)
        print(f"✅ 任务创建成功")
        print(f"  任务ID: {new_task.id}")
        print(f"  关联项目ID: {new_task.project_id}")
        print(f"  任务名称: {new_task.name}")
        
        # 清理测试数据
        print(f"\n=== 7. 清理测试数据 ===")
        
        # 删除测试任务
        crud.delete_task(db, new_task.id)
        print(f"✅ 测试任务已删除")
        
        # 删除测试项目
        deleted_project = crud.delete_project(db, project_id)
        if deleted_project:
            print(f"✅ 项目删除成功")
        else:
            print("❌ 项目删除失败")
            
        # 删除测试用户
        crud.delete_user(db, test_user.id)
        print(f"✅ 测试用户已删除")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_project_crud()
