#!/usr/bin/env python3
"""
用户接口调试运行脚本
启动服务器并运行用户API测试
"""

import subprocess
import time
import sys
import os
import signal
import requests
from threading import Thread

def start_server():
    """启动FastAPI服务器"""
    print("正在启动FastAPI服务器...")
    try:
        # 启动服务器
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        return process
    except Exception as e:
        print(f"启动服务器失败: {e}")
        return None

def wait_for_server(max_wait=30):
    """等待服务器启动"""
    print("等待服务器启动...")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://localhost:8000/docs", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器启动成功!")
                return True
        except:
            pass
        
        print(f"等待中... ({i+1}/{max_wait})")
        time.sleep(1)
    
    print("❌ 服务器启动超时")
    return False

def run_tests():
    """运行测试"""
    print("\n开始运行用户API测试...")
    try:
        result = subprocess.run([sys.executable, "test_user_api.py"], 
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"运行测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 用户接口调试工具 ===")
    
    # 检查必要文件
    required_files = ["main.py", "test_user_api.py", "models.py", "crud.py", "routers/users.py"]
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            sys.exit(1)
    
    server_process = None
    
    try:
        # 启动服务器
        server_process = start_server()
        if not server_process:
            print("❌ 无法启动服务器")
            sys.exit(1)
        
        # 等待服务器启动
        if not wait_for_server():
            print("❌ 服务器启动失败")
            sys.exit(1)
        
        # 运行测试
        success = run_tests()
        
        if success:
            print("\n✅ 所有测试完成")
        else:
            print("\n❌ 测试过程中出现错误")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    
    finally:
        # 清理服务器进程
        if server_process:
            print("\n正在关闭服务器...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()
            print("服务器已关闭")

if __name__ == "__main__":
    main()
