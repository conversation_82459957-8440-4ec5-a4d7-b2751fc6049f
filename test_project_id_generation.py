#!/usr/bin/env python3
"""
测试新的项目ID生成方式
验证P前缀+时间戳+随机数的ID生成是否正常工作
"""

import random
from datetime import datetime

def generate_project_id():
    """生成新的项目ID：P + 时间戳 + 随机数"""
    return f"P{int(datetime.now().timestamp() * 1000)}{random.randint(1000, 9999)}"

def generate_task_id():
    """生成任务ID：T + 时间戳 + 随机数"""
    return f"T{int(datetime.now().timestamp() * 1000)}{random.randint(1000, 9999)}"

def generate_user_id():
    """生成用户ID：时间戳 + 随机数"""
    return f"{int(datetime.now().timestamp() * 1000)}{random.randint(1000, 9999)}"

def test_id_generation():
    """测试ID生成"""
    print("=== 测试新的项目ID生成方式 ===")
    print("格式：P + 时间戳(毫秒) + 随机数(4位)")
    print()
    
    # 生成10个示例项目ID
    project_ids = []
    for i in range(10):
        project_id = generate_project_id()
        project_ids.append(project_id)
        print(f"项目ID {i+1:2d}: {project_id}")
    
    print()
    print("=== 项目ID长度统计 ===")
    lengths = [len(id) for id in project_ids]
    print(f"最短长度: {min(lengths)}")
    print(f"最长长度: {max(lengths)}")
    print(f"平均长度: {sum(lengths)/len(lengths):.1f}")
    
    print()
    print("=== 与其他ID类型对比 ===")
    user_id = generate_user_id()
    task_id = generate_task_id()
    project_id = generate_project_id()
    
    print(f"用户ID:   {user_id}")
    print(f"任务ID:   {task_id}")
    print(f"项目ID:   {project_id}")
    print()
    print(f"用户ID长度: {len(user_id)}")
    print(f"任务ID长度: {len(task_id)}")
    print(f"项目ID长度: {len(project_id)}")
    
    print()
    print("=== ID格式验证 ===")
    for i, pid in enumerate(project_ids[:3], 1):
        print(f"项目ID {i}: {pid}")
        if pid.startswith('P') and len(pid) == 18 and pid[1:].isdigit():
            timestamp_part = pid[1:14]
            random_part = pid[14:]
            print(f"  ✅ 格式正确 - 前缀:P, 时间戳:{timestamp_part}, 随机数:{random_part}")
        else:
            print(f"  ❌ 格式错误")
    
    print()
    print("=== 与旧方式对比 ===")
    import uuid
    old_project_id = f"{int(datetime.now().timestamp() * 1000)}-{uuid.uuid4().hex}"
    new_project_id = generate_project_id()
    
    print(f"旧方式 (时间戳-UUID): {old_project_id}")
    print(f"新方式 (P+时间戳+随机数): {new_project_id}")
    print(f"长度减少: {len(old_project_id) - len(new_project_id)} 个字符")
    print(f"长度减少比例: {((len(old_project_id) - len(new_project_id)) / len(old_project_id) * 100):.1f}%")
    
    print()
    print("=== ID系统总览 ===")
    print("| 实体类型 | 前缀 | 示例 | 长度 | 用途 |")
    print("|----------|------|------|------|------|")
    print(f"| 用户     | 无   | {user_id} | {len(user_id)}位 | 用户标识 |")
    print(f"| 任务     | T    | {task_id} | {len(task_id)}位 | 任务标识 |")
    print(f"| 项目     | P    | {project_id} | {len(project_id)}位 | 项目标识 |")
    
    print()
    print("=== 优势总结 ===")
    print("✅ 易于识别：P前缀明确标识项目ID")
    print("✅ 调试友好：在日志、URL中容易区分")
    print("✅ 保持唯一性：时间戳+随机数确保唯一性")
    print("✅ 长度适中：18位，比UUID方案短很多")
    print("✅ 系统统一：与任务ID格式保持一致")

if __name__ == "__main__":
    test_id_generation()
