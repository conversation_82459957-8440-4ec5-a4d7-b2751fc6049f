#!/usr/bin/env python3
"""
测试使用新任务ID的任务CRUD操作
验证任务的创建、查询、更新、删除功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from database import SessionLocal, init_db
from models import Task, User, Project
from schemas import TaskCreate, TaskUpdate
import crud

def test_task_crud():
    """测试任务CRUD操作"""
    print("=== 测试新任务ID的CRUD操作 ===")
    
    # 初始化数据库
    init_db()
    
    # 创建数据库会话
    db: Session = SessionLocal()
    
    try:
        # 首先创建一个测试用户（如果不存在）
        test_user = db.query(User).filter(User.username == "test_user").first()
        if not test_user:
            from schemas import UserCreate
            test_user_data = UserCreate(
                username="test_user",
                password="test123",
                role="user"
            )
            test_user = crud.create_user(db, test_user_data)
            print(f"✅ 创建测试用户: {test_user.id}")
        
        # 创建测试任务数据
        test_task_data = TaskCreate(
            project_id="test_project_123",  # 假设的项目ID
            name="测试任务 - 新ID格式",
            priority="高",
            progress=0.0,
            owner_id=test_user.id
        )
        
        print(f"\n=== 1. 创建任务 ===")
        print(f"任务名称: {test_task_data.name}")
        print(f"负责人: {test_user.username}")
        
        # 创建任务
        new_task = crud.create_task(db, test_task_data)
        
        print(f"✅ 任务创建成功!")
        print(f"任务ID: {new_task.id}")
        print(f"任务名称: {new_task.name}")
        print(f"优先级: {new_task.priority}")
        print(f"负责人ID: {new_task.owner_id}")
        
        # 验证任务ID格式
        print(f"\n=== 2. 验证任务ID格式 ===")
        task_id = new_task.id
        if task_id.startswith('T') and len(task_id) == 18 and task_id[1:].isdigit():
            timestamp_part = task_id[1:14]
            random_part = task_id[14:]
            print(f"✅ 任务ID格式正确")
            print(f"  完整ID: {task_id}")
            print(f"  前缀: T")
            print(f"  时间戳: {timestamp_part}")
            print(f"  随机数: {random_part}")
        else:
            print(f"❌ 任务ID格式错误: {task_id}")
            
        # 测试查询任务
        print(f"\n=== 3. 查询任务 ===")
        found_task = crud.get_task(db, task_id)
        if found_task:
            print(f"✅ 任务查询成功: {found_task.name}")
            print(f"  任务ID: {found_task.id}")
        else:
            print("❌ 任务查询失败")
            
        # 测试更新任务
        print(f"\n=== 4. 更新任务 ===")
        update_data = TaskUpdate(
            project_id=test_task_data.project_id,
            name="更新后的任务名称",
            priority="中",
            progress=50.0,
            owner_id=test_user.id
        )
        
        updated_task = crud.update_task(task_id, update_data, db)
        if updated_task:
            print(f"✅ 任务更新成功")
            print(f"  新名称: {updated_task.name}")
            print(f"  新优先级: {updated_task.priority}")
            print(f"  新进度: {updated_task.progress}%")
        else:
            print("❌ 任务更新失败")
            
        # 测试任务列表查询
        print(f"\n=== 5. 查询任务列表 ===")
        tasks = crud.get_tasks(db, skip=0, limit=10)
        print(f"✅ 查询到 {len(tasks)} 个任务")
        for i, task in enumerate(tasks[:3], 1):  # 只显示前3个
            print(f"  任务{i}: {task.id} - {task.name}")
            
        # 清理测试数据
        print(f"\n=== 6. 清理测试数据 ===")
        deleted_task = crud.delete_task(db, task_id)
        if deleted_task:
            print(f"✅ 任务删除成功: {deleted_task.name}")
        else:
            print("❌ 任务删除失败")
            
        # 删除测试用户
        crud.delete_user(db, test_user.id)
        print(f"✅ 测试用户已删除")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_task_crud()
