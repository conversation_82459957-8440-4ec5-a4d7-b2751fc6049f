import sqlite3
import os
def check_table_structure(db_path, table_name):
    """检查指定数据库中表的结构"""
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            print(f"数据库文件 {db_path} 不存在")
            return None
            
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 执行PRAGMA table_info命令
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # 如果表不存在，查看所有表
        if not columns:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            all_tables = cursor.fetchall()
            print(f"表 {table_name} 不存在")
            print("数据库中的表:", all_tables)
            return None
        
        # 打印表结构信息
        print(f"\n{table_name} 表结构:")
        print("cid | name | type | notnull | dflt_value | pk")
        print("-----------------------------------------------")
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]} | {column[3]} | {column[4]} | {column[5]}")
            
        return columns
            
    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        return None
    finally:
        if conn:
            conn.close()

def list_all_tables(db_path):
    """列出数据库中的所有表"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("\n数据库中的所有表:")
        for table in tables:
            print(f"- {table[0]}")
            
        return tables
            
    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        return None
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    db_path = "backend.db"
    table_name = "users"
    
    # 检查users表结构
    check_table_structure(db_path, table_name)
    
    # 列出数据库中的所有表
    list_all_tables(db_path)