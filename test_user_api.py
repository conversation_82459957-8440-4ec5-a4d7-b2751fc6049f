#!/usr/bin/env python3
"""
用户API接口测试脚本
测试用户的增删改查功能
"""

import requests
import json
import sys

# API基础URL
BASE_URL = "http://localhost:8000"

def test_create_user():
    """测试创建用户"""
    print("=== 测试创建用户 ===")
    
    # 测试数据
    user_data = {
        "username": "test_user_001",
        "password": "test123456",
        "role": "user",
        "status": "启用"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=user_data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户创建成功")
                return result.get("data", {}).get("id")
            else:
                print(f"❌ 用户创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_get_users():
    """测试获取用户列表"""
    print("\n=== 测试获取用户列表 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print(f"✅ 获取用户列表成功，共 {len(result.get('data', []))} 个用户")
            else:
                print(f"❌ 获取用户列表失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_get_user_info(user_id):
    """测试获取用户信息"""
    print(f"\n=== 测试获取用户信息 (ID: {user_id}) ===")
    
    if not user_id:
        print("❌ 用户ID为空，跳过测试")
        return
    
    try:
        response = requests.get(f"{BASE_URL}/users/info", params={"user_id": user_id})
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 获取用户信息成功")
            else:
                print(f"❌ 获取用户信息失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_update_user(user_id):
    """测试更新用户信息"""
    print(f"\n=== 测试更新用户信息 (ID: {user_id}) ===")
    
    if not user_id:
        print("❌ 用户ID为空，跳过测试")
        return
    
    # 更新数据
    update_data = {
        "username": "test_user_001_updated",
        "password": "new_password123",
        "role": "admin",
        "status": "启用"
    }
    
    try:
        response = requests.put(f"{BASE_URL}/users/info", 
                              params={"user_id": user_id}, 
                              json=update_data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户更新成功")
            else:
                print(f"❌ 用户更新失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_login():
    """测试用户登录"""
    print("\n=== 测试用户登录 ===")
    
    # 登录数据
    login_data = {
        "username": "test_user_001_updated",
        "password": "new_password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/login", json=login_data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户登录成功")
                return result.get("data", {}).get("access_token")
            else:
                print(f"❌ 用户登录失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_delete_user(user_id):
    """测试删除用户"""
    print(f"\n=== 测试删除用户 (ID: {user_id}) ===")
    
    if not user_id:
        print("❌ 用户ID为空，跳过测试")
        return
    
    try:
        response = requests.delete(f"{BASE_URL}/users/{user_id}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户删除成功")
            else:
                print(f"❌ 用户删除失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主测试函数"""
    print("开始测试用户API接口...")
    print("请确保服务器已启动在 http://localhost:8000")
    
    # 测试服务器连接
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code != 200:
            print("❌ 无法连接到服务器，请检查服务器是否启动")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        sys.exit(1)
    
    # 执行测试
    user_id = test_create_user()
    test_get_users()
    test_get_user_info(user_id)
    test_update_user(user_id)
    test_login()
    test_delete_user(user_id)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
