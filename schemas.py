from pydantic import BaseModel
from typing import Optional, List, Any, Union
from datetime import date
from enum import Enum

class UserBase(BaseModel):
    username: str
    role: Optional[str] = None
    status: Optional[Union[str, int]] = "禁用"  # 兼容前端传int

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: str
    class Config:
        from_attributes = True

class LoginCredentials(BaseModel):
    username: str
    password: str

# ProjectBase
class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    target: Optional[int] = None
    progress: Optional[float] = 0.0
    owner_id: Optional[str] = None

# ProjectCreate
class ProjectCreate(ProjectBase):
    pass

# Project
class Project(ProjectBase):
    id: str
    class Config:
        from_attributes = True

class ProjectUpdate(ProjectBase):
    pass


class TaskBase(BaseModel):
    project_id: str
    name: str
    priority: Optional[str] = None
    progress: Optional[float] = 0.0
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    owner_id: Optional[str] = None
    related_task: Optional[str] = None

class TaskCreate(TaskBase):
    pass

class Task(TaskBase):
    id: str
    class Config:
        from_attributes = True

class TaskUpdate(TaskBase):
    pass

class BaseResponse(BaseModel):
    code: int
    msg: str
    data: Any

class ListResponse(BaseResponse):
    data: List[Any]

class DetailResponse(BaseResponse):
    data: Any

class TimeRange(str, Enum):
    TODAY = "today"
    WEEK = "week"
    MONTH = "month"
    CUSTOM = "custom"

class TaskStatsResponse(BaseModel):
    total_count: int
    tasks: List[dict]