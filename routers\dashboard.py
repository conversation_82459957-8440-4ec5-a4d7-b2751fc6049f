from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func
import models
from database import get_db

router = APIRouter(
    prefix="/dashboard",
    tags=["dashboard"]
)
@router.get("/overview")
def dashboard_overview(db: Session = Depends(get_db)):
    """
    Dashboard Overview
    """
    project_count = db.query(models.Project).count()
    task_count = db.query(models.Task).count()
    user_count = db.query(models.User).count()
    
    # 项目进度平均值
    avg_progress = db.query(models.Project).with_entities(models.Project.progress).all()
    avg_progress = round(sum([p[0] for p in avg_progress]) / project_count, 2) if project_count else 0
    
    # 各项目任务数
    project_tasks_subquery = db.query(models.Task.project_id, func.count().label('task_count')).group_by(models.Task.project_id).subquery()
    project_tasks = db.query(models.Project.name, project_tasks_subquery.c.task_count).join(project_tasks_subquery, models.Project.id == project_tasks_subquery.c.project_id).all()
    
    # 各人员任务数
    user_tasks_subquery = db.query(models.Task.owner_id, func.count().label('task_count')).group_by(models.Task.owner_id).subquery()
    user_tasks = db.query(models.User.name, user_tasks_subquery.c.task_count).join(user_tasks_subquery, models.User.id == user_tasks_subquery.c.owner_id).all()
    return {
        "project_count": project_count,
        "task_count": task_count,
        "user_count": user_count,
        "avg_progress": avg_progress,
        "project_tasks": project_tasks,
        "user_tasks": user_tasks
    } 